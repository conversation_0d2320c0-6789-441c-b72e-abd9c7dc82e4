import 'package:aichat/data/const.dart';
import 'package:aichat/data/models/ai_model.dart';
import 'package:aichat/ui/widgets/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../core/controllers/page/profile_controller.dart';
import '../../theme/light_dark/theme.dart';
import '../../widgets/checkin/checkin_entry_widget.dart';

class ProfilePage extends StatefulWidget {
  @override
  State<ProfilePage> createState() => ProfilePageState();
}

class ProfilePageState extends State<ProfilePage> {
  final ProfileController controller = Get.find();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () async {
      await controller.refreshKeyInfo(); // 每次打开页面刷新密钥/额度信息
      await controller.loadModels(); // 每次打开页面刷新模型信息
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: const Text('个人中心'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          Get.isDarkMode
              ? IconButton(
                  icon: const Icon(Icons.light_mode),
                  onPressed: () {
                    Get.changeTheme(AppTheme.light);
                  },
                )
              : IconButton(
                  icon: const Icon(Icons.nightlight),
                  onPressed: () {
                    Get.changeTheme(AppTheme.dark);
                  },
                ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Get.toNamed(ConstData.settingPage);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 模型选择区域
              _buildSectionTitle('选择模型'),
              const SizedBox(height: 16),
              _buildModelSelectionSection(),

              const SizedBox(height: 24),

              // 账号信息区域
              _buildSectionTitle('账号信息'),
              const SizedBox(height: 16),
              _buildAccountInfoSection(),

              const SizedBox(height: 24),

              // {{ AURA-X: Add - 添加签到功能入口. Approval: 寸止(ID:2025-08-02T23:31:02+08:00). }}
              // 签到功能区域
              _buildSectionTitle('每日签到'),
              const SizedBox(height: 16),
              const CheckinEntryWidget(),

              const SizedBox(height: 24),

              // 额度信息区域
              _buildSectionTitle('额度信息'),
              const SizedBox(height: 16),
              _buildQuotaInfoSection(),

              const SizedBox(height: 24),

              // 付费入口
              _buildPaymentSection(context),

              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  // 构建区域标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  // 模型选择区域
  Widget _buildModelSelectionSection() {
    return Obx(() {
      if (controller.models.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 30),
            child: CircularProgressIndicator(),
          ),
        );
      }

      return SizedBox(
        height: 240,
        child: PageView.builder(
          controller: PageController(viewportFraction: 0.85),
          itemCount: controller.models.length,
          itemBuilder: (context, index) {
            final model = controller.models[index];
            return _buildModelCard(context, model);
          },
        ),
      );
    });
  }

  // 模型卡片
  Widget _buildModelCard(BuildContext context, dynamic model) {
    // {{ AURA-X: Modify - 使用新的模型数据格式，支持价格字段和标签. Approval: 寸止(ID:2025-08-04). }}
    String modelName = model['name'] ?? '未知模型';
    String modelDescription = model['descriptions'] ?? '暂无描述';

    // 获取新的价格格式
    double inputPrice = (model['input_price_per_mtokens'] ?? 0.0).toDouble();
    double outputPrice = (model['output_price_per_mtokens'] ?? 0.0).toDouble();
    String priceTip = model['price_tip'] ?? '';
    List<String> tags = List<String>.from(model['tags'] ?? []);

    String modelImage = 'assets/emojis/ok.png'; // 默认图片

    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(
              color: controller.currentModelName.value == model['name']
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.outlineVariant,
              width: 1.5,
            ),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(24),
            // {{ AURA-X: Modify - 添加长按显示详情弹窗，短按选择模型. Approval: 寸止(ID:2025-08-04). }}
            onTap: () {
              final modelName = model['name'];
              if (modelName != null) {
                controller.setCurrentModel(model);
                Toast.showSuccess('切换成功', '当前模型：$modelName');
              }
            },
            onLongPress: () {
              _showModelDetailDialog(context, model);
            },
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部区域：模型名称、状态和图标
                  Row(
                    children: [
                      // ClipRRect(
                      //   borderRadius: BorderRadius.circular(12),
                      //   child: Image.asset(
                      //     modelImage,
                      //     width: 48,
                      //     height: 48,
                      //     fit: BoxFit.cover,
                      //   ),
                      // ),
                      // const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          modelName,
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: controller.currentModelName.value ==
                                    model['name']
                                ? Theme.of(context).primaryColor
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ),
                      if (controller.currentModelName.value == model['name'])
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '使用中',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),

                  // {{ AURA-X: Modify - 更新价格显示，支持输入/输出价格分别显示和标签. Approval: 寸止(ID:2025-08-04). }}
                  // 价格和标签区域
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 价格显示
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 3),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.attach_money,
                                    size: 12,
                                    color: Colors.amber.shade800,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    (inputPrice == 0.0 && outputPrice == 0.0)
                                        ? '免费'
                                        : '${inputPrice.toStringAsFixed(2)}/M',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.amber.shade800,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (inputPrice == 0.0 && outputPrice == 0.0)
                              Container(
                                margin: const EdgeInsets.only(left: 8),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 3),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  "免费使用",
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.green[600],
                                  ),
                                ),
                              ),
                          ],
                        ),
                        // 标签显示
                        if (tags.isNotEmpty) ...[
                          const SizedBox(height: 6),
                          Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: tags.take(3).map((tag) {
                              final tagStyle = AIModel.getTagStyle(tag);
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Color(tagStyle['color']).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: Color(tagStyle['color']).withOpacity(0.3),
                                    width: 0.5,
                                  ),
                                ),
                                child: Text(
                                  tagStyle['text'],
                                  style: TextStyle(
                                    fontSize: 8,
                                    fontWeight: FontWeight.w500,
                                    color: Color(tagStyle['color']),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // 描述区域 - 添加滚动功能
                  Expanded(
                    child: Stack(
                      children: [
                        // 滚动内容
                        NotificationListener<ScrollNotification>(
                          // 阻止滚动事件冒泡到外层PageView
                          onNotification: (notification) => true,
                          child: GestureDetector(
                            // 避免点击在文本区域时触发卡片的onTap
                            onTap: () {},
                            // 确保垂直滚动的手势不被PageView捕获
                            behavior: HitTestBehavior.opaque,
                            child: SingleChildScrollView(
                              physics: const BouncingScrollPhysics(),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 使用语义标签提升无障碍特性
                                  Semantics(
                                    label: '模型描述',
                                    hint: '可上下滑动查看完整描述',
                                    child: Text(
                                      modelDescription,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                                  // 添加底部间距，确保滚动到底部时内容不会被渐变遮罩挡住
                                  const SizedBox(height: 28),
                                ],
                              ),
                            ),
                          ),
                        ),

                        // 底部渐变遮罩，提示可以滚动
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: 28,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer
                                      .withOpacity(0.0),
                                  Theme.of(context)
                                      .colorScheme
                                      .surfaceContainer,
                                ],
                              ),
                            ),
                            // 添加滚动提示图标
                            alignment: Alignment.center,
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              size: 16,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  // 账号信息区域
  Widget _buildAccountInfoSection() {
    return Obx(() => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainer,
            borderRadius: BorderRadius.circular(16),
            // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                _buildInfoRow(
                  icon: Icons.person,
                  title: '账号 ID',
                  value: controller.accountId.value,
                  canCopy: true,
                ),
                const Divider(height: 24),
                _buildKeyInfoRow(),
              ],
            ),
          ),
        ));
  }

  // 信息行组件
  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String value,
    bool canCopy = false,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).primaryColor,
            size: 22,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),
        if (canCopy)
          IconButton(
            icon: const Icon(Icons.copy, size: 20),
            onPressed: () async {
              // 复制到剪贴板
              await Clipboard.setData(ClipboardData(text: value));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('已复制$title')),
              );
            },
          ),
      ],
    );
  }

  // 密钥信息行组件（带显示/隐藏功能）
  Widget _buildKeyInfoRow() {
    return Obx(() => Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.key,
                color: Theme.of(context).primaryColor,
                size: 22,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '密钥',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // 使用可选择的文本组件，支持长按选择
                  SelectableText(
                    controller.displayKeyValue,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                      fontFamily: 'monospace', // 使用等宽字体，让密钥显示更整齐
                    ),
                    maxLines: 2, // 允许最多2行显示
                  ),
                ],
              ),
            ),
            // 显示/隐藏按钮
            IconButton(
              icon: Icon(
                controller.isKeyVisible.value
                    ? Icons.visibility_off
                    : Icons.visibility,
                size: 20,
              ),
              onPressed: controller.toggleKeyVisibility,
              tooltip: controller.isKeyVisible.value ? '隐藏密钥' : '显示密钥',
            ),
            // 复制按钮
            IconButton(
              icon: const Icon(Icons.copy, size: 20),
              onPressed: () async {
                // 复制完整的密钥值到剪贴板
                await Clipboard.setData(
                    ClipboardData(text: controller.keyValue.value));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('已复制密钥')),
                );
              },
              tooltip: '复制密钥',
            ),
          ],
        ));
  }

  // 额度信息区域
  Widget _buildQuotaInfoSection() {
    return Obx(() {
      final int total = controller.totalQuota.value;
      final int used = controller.usedQuota.value;
      final int remaining = controller.remainingQuota.value;

      // 防止除零错误
      double usedPercentage = 0.0;
      if (total > 0) {
        usedPercentage = used / total;
        // 确保百分比在0-1之间
        usedPercentage = usedPercentage.clamp(0.0, 1.0);
      }

      return Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(16),
          // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 进度条
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '已使用 ${(usedPercentage * 100).toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: _getQuotaColor(usedPercentage),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: LinearProgressIndicator(
                      value: usedPercentage,
                      backgroundColor:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      valueColor: AlwaysStoppedAnimation<Color>(
                          _getQuotaColor(usedPercentage)),
                      minHeight: 8,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // 额度详情
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuotaDetailItem(
                    icon: Icons.data_usage,
                    title: '总额度',
                    value: _formatNumber(total),
                  ),
                  _buildQuotaDetailItem(
                    icon: Icons.check_circle_outline,
                    title: '剩余额度',
                    value: _formatNumber(remaining),
                    valueColor: Colors.green,
                  ),
                  _buildQuotaDetailItem(
                    icon: Icons.history,
                    title: '已用额度',
                    value: _formatNumber(used),
                    valueColor: _getQuotaColor(usedPercentage),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  // 额度详情项
  Widget _buildQuotaDetailItem({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Column(
      children: [
        Icon(icon,
            color:
                valueColor ?? Theme.of(context).colorScheme.onSurfaceVariant),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: valueColor ?? Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // 获取额度对应的颜色
  Color _getQuotaColor(double percentage) {
    if (percentage > 0.8) {
      return Colors.red;
    } else if (percentage > 0.6) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  // 格式化数字
  // 把额度转换为特定单位后再显示出来
  String _formatNumber(int number) {
    return "${((number / 500000) < 0 ? 0 : (number / 500000)).toStringAsFixed(2)}￥";
  }

  // 付费入口
  Widget _buildPaymentSection(context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.payments_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '额度充值',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 6),
                      Text(
                        '购买额度以继续使用AI聊天服务',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.credit_card),
                    label: const Text('卡密充值'),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () {
                      _showRechargeDialog(context);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('购买卡密'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: () {
                      controller.openPaymentWebsite();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 显示充值对话框
  void _showRechargeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('卡密充值'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('请输入您的卡密:'),
              const SizedBox(height: 16),
              TextField(
                controller: controller.cardCodeController,
                decoration: const InputDecoration(
                  hintText: '请输入卡密',
                  // border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => controller.message.value.isNotEmpty
                  ? Flexible(
                      child: Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: controller.message.value.contains('成功')
                              ? Colors.green.withOpacity(0.1)
                              : Theme.of(context)
                                  .colorScheme
                                  .error
                                  .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: controller.message.value.contains('成功')
                                ? Colors.green.withOpacity(0.3)
                                : Theme.of(context)
                                    .colorScheme
                                    .error
                                    .withOpacity(0.3),
                          ),
                        ),
                        constraints: const BoxConstraints(
                          maxHeight: 120, // 限制最大高度
                        ),
                        child: SingleChildScrollView(
                          child: Text(
                            controller.message.value,
                            style: TextStyle(
                              color: controller.message.value.contains('成功')
                                  ? Colors.green
                                  : Theme.of(context).colorScheme.error,
                              fontSize: 14,
                              height: 1.4, // 增加行高提高可读性
                            ),
                          ),
                        ),
                      ),
                    )
                  : const SizedBox()),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              controller.message.value = '';
            },
            child: const Text('取消'),
          ),
          Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : () async {
                        await controller.addKeyQuota();
                        if (controller.message.value.contains('成功')) {
                          Future.delayed(const Duration(seconds: 1), () {
                            Navigator.pop(context);
                            controller.message.value = '';
                          });
                        }
                      },
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('确认充值'),
              )),
        ],
      ),
    );
  }

  // {{ AURA-X: Add - 添加模型详情弹窗方法. Approval: 寸止(ID:2025-08-04). }}
  void _showModelDetailDialog(BuildContext context, dynamic model) {
    String modelName = model['name'] ?? '未知模型';
    String modelDescription = model['descriptions'] ?? '暂无描述';
    double inputPrice = (model['input_price_per_mtokens'] ?? 0.0).toDouble();
    double outputPrice = (model['output_price_per_mtokens'] ?? 0.0).toDouble();
    String priceTip = model['price_tip'] ?? '';
    List<String> tags = List<String>.from(model['tags'] ?? []);
    int maxToken = model['maxToken'] ?? 0;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.smart_toy,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  modelName,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标签区域
                if (tags.isNotEmpty) ...[
                  const Text(
                    '标签',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: tags.map((tag) {
                      final tagStyle = AIModel.getTagStyle(tag);
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Color(tagStyle['color']).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Color(tagStyle['color']).withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          tagStyle['text'],
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(tagStyle['color']),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                ],

                // 价格信息
                const Text(
                  '价格信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      if (inputPrice == 0.0 && outputPrice == 0.0)
                        Row(
                          children: [
                            Icon(Icons.free_breakfast, color: Colors.green, size: 20),
                            const SizedBox(width: 8),
                            const Text(
                              '免费使用',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        )
                      else ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('输入价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                            Text('${inputPrice.toStringAsFixed(2)} / 百万tokens'),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('输出价格:', style: TextStyle(fontWeight: FontWeight.w500)),
                            Text('${outputPrice.toStringAsFixed(2)} / 百万tokens'),
                          ],
                        ),
                      ],
                      if (priceTip.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline, color: Colors.blue, size: 16),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  priceTip,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 最大Token数
                const Text(
                  '技术规格',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('最大Token数:', style: TextStyle(fontWeight: FontWeight.w500)),
                      Text('${maxToken.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}'),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 模型描述
                const Text(
                  '模型描述',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    modelDescription,
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
            ElevatedButton(
              onPressed: () {
                controller.setCurrentModel(model);
                Navigator.of(context).pop();
                Toast.showSuccess('切换成功', '当前模型：$modelName');
              },
              child: const Text('选择此模型'),
            ),
          ],
        );
      },
    );
  }
}
