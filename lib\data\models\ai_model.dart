// {{ AURA-X: Add - 创建AI模型数据类以适配新的API响应格式. Approval: 寸止(ID:2025-08-04). }}
class AIModel {
  final String name;
  final double inputPricePerMtokens;  // 输入价格，每百万token
  final double outputPricePerMtokens; // 输出价格，每百万token
  final List<String> tags;            // 模型标签
  final String priceTip;              // 价格变化提示信息
  final String descriptions;          // 模型详细描述
  final int maxToken;                 // 最大token数

  AIModel({
    required this.name,
    required this.inputPricePerMtokens,
    required this.outputPricePerMtokens,
    required this.tags,
    required this.priceTip,
    required this.descriptions,
    required this.maxToken,
  });

  factory AIModel.fromJson(Map<String, dynamic> json) {
    return AIModel(
      name: json['name'] ?? '',
      inputPricePerMtokens: (json['input_price_per_mtokens'] ?? 0.0).toDouble(),
      outputPricePerMtokens: (json['output_price_per_mtokens'] ?? 0.0).toDouble(),
      tags: List<String>.from(json['tags'] ?? []),
      priceTip: json['price_tip'] ?? '',
      descriptions: json['descriptions'] ?? '',
      maxToken: json['maxToken'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'input_price_per_mtokens': inputPricePerMtokens,
      'output_price_per_mtokens': outputPricePerMtokens,
      'tags': tags,
      'price_tip': priceTip,
      'descriptions': descriptions,
      'maxToken': maxToken,
    };
  }

  // 为了向后兼容，提供旧格式的price字段（使用输入价格作为默认价格）
  double get price => inputPricePerMtokens;

  // 获取价格显示文本
  String get priceDisplayText {
    if (inputPricePerMtokens == 0.0 && outputPricePerMtokens == 0.0) {
      return '免费';
    }
    return '输入: ${inputPricePerMtokens.toStringAsFixed(2)}/M • 输出: ${outputPricePerMtokens.toStringAsFixed(2)}/M';
  }

  // 获取简化的价格显示（用于卡片）
  String get simplePriceText {
    if (inputPricePerMtokens == 0.0 && outputPricePerMtokens == 0.0) {
      return '免费';
    }
    // 显示输入价格作为主要价格参考
    return '${inputPricePerMtokens.toStringAsFixed(2)}/M tokens';
  }

  // 检查是否有特定标签
  bool hasTag(String tag) {
    return tags.contains(tag);
  }

  // 获取标签颜色（根据标签类型返回不同颜色）
  static Map<String, dynamic> getTagStyle(String tag) {
    switch (tag) {
      case '特价':
        return {'color': 0xFFFF5722, 'text': '🔥 特价'};
      case '推荐':
        return {'color': 0xFF4CAF50, 'text': '⭐ 推荐'};
      case '高性能':
        return {'color': 0xFF2196F3, 'text': '⚡ 高性能'};
      case '国产':
        return {'color': 0xFF9C27B0, 'text': '🇨🇳 国产'};
      case '浮动':
        return {'color': 0xFFFF9800, 'text': '📈 浮动'};
      default:
        return {'color': 0xFF757575, 'text': tag};
    }
  }
}

// AI模型列表响应类
class AIModelListResponse {
  final List<AIModel> aiModelList;

  AIModelListResponse({
    required this.aiModelList,
  });

  factory AIModelListResponse.fromJson(Map<String, dynamic> json) {
    var modelsList = json['aiModelList'] as List? ?? [];
    List<AIModel> models = modelsList
        .map((item) => AIModel.fromJson(item))
        .toList();

    return AIModelListResponse(
      aiModelList: models,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'aiModelList': aiModelList.map((model) => model.toJson()).toList(),
    };
  }
}
