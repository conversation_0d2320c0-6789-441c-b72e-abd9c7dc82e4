import 'dart:convert';
import 'dart:core';

import 'package:aichat/api/until.dart';
import 'package:dio/dio.dart';
import 'package:hive/hive.dart';

import '../data/const.dart';
import '../data/hive/curd.dart';
import '../data/models/api_defult_model.dart';
import '../data/models/ai_model.dart';
import '../data/models/announcement_response.dart';
import 'http/dio_service.dart';

class Client {
  Dio dio = DioService.instance;

  // ai部分的请求获取
  // 获取到所有角色的提示词信息
  Future<void> getPrompt() async {
    Response response = await dio.get('/ai/');

    APIModel result = response.data;

    // 检查是否为未登录响应
    if (result.code == 401) {
      print('ℹ️ 获取提示词需要登录，跳过此操作');
      return; // 静默返回，不抛出异常
    }

    if (result.isSuccess && result.data != null) {
      // 进行解密数据
      Map prompt = EncryptionUtil.decryptData(result.data.toString());
      // 获取到云端的信息后储存到本地hive中
      await Hive.openBox(ConstData.settingBox);
      // 进行赋值
      Box settingBox = Hive.box(ConstData.settingBox);
      // 删除原有数据
      settingBox.delete(ConstData.promptKey);
      // 更新储存数据
      settingBox.put(ConstData.promptKey, prompt);
      print('读取云端提示词成功');
    } else {
      print('请求提示词失败: ${result.errorMessage}');
    }
  }

  // 获取模型列表以及基本信息
  Future<void> getModels() async {
    var response = await dio.get('/ai/models');
    APIModel result = response.data;

    // 检查是否为未登录响应
    if (result.code == 401) {
      print('ℹ️ 获取模型列表需要登录，跳过此操作');
      return; // 静默返回，不抛出异常
    }

    if (result.isSuccess && result.data != null) {
      // {{ AURA-X: Modify - 解析新的API响应格式aiModelList结构. Approval: 寸止(ID:2025-08-04). }}
      try {
        // {{ AURA-X: Modify - 解析新的API响应格式aiModelList结构. Approval: 寸止(ID:2025-08-04). }}
        // 解析新的API响应格式
        AIModelListResponse modelListResponse = AIModelListResponse.fromJson(result.data);
        List<AIModel> aiModels = modelListResponse.aiModelList;

        // 获取到云端的信息后储存到本地hive中
        await Hive.openBox(ConstData.settingBox);
        Box settingBox = Hive.box(ConstData.settingBox);

        // 将模型数据转换为JSON格式存储
        List<Map<String, dynamic>> modelsForStorage = aiModels.map((model) => model.toJson()).toList();

        // 储存数据到本地
        await settingBox.put(ConstData.allModels, modelsForStorage);
        print('储存所有模型成功，共${aiModels.length}个模型');

        // 如果未找到保存的模型或没有选中模型，则默认选第一个
        if (settingBox.get(ConstData.currentModel) == null && aiModels.isNotEmpty) {
          await settingBox.put(ConstData.currentModel, modelsForStorage[0]);
        }

        // 打印模型信息
        for (var model in aiModels) {
          print('读取储存模型：${model.name}，输入价格：${model.inputPricePerMtokens}/M，输出价格：${model.outputPricePerMtokens}/M，标签：${model.tags.join(', ')}，描述：${model.descriptions}，最大token：${model.maxToken}');
        }
      } catch (e, s) {
        print('解析模型数据失败: $e');
        print('堆栈跟踪: $s');
        print('原始数据类型: ${result.data.runtimeType}');
        print('原始数据内容: ${result.data}');
      }
    } else {
      print('请求模型失败: ${result.errorMessage}');
    }
  }



  Future<APIModel> register(String email) async {
    Response response =
        await dio.get('/register', queryParameters: {'email': email});
    return response.data;
  }

  // 邮箱验证账号
  Future<APIModel> verifyRegister(
      String username, String password, String code) async {
    var data = json.encode({"username": username, "password": password});

    Response response = await dio.post(
      '/register/',
      data: data,
      queryParameters: {"code_in": code},
    );
    return response.data;
  }

  // 邮箱登录账号
  Future<APIModel> login(String username, String password) async {
    var data = json.encode({"username": username, "password": password});

    Response response = await dio.post(
      '/login/',
      data: data,
      options: Options(headers: {'Content-Type': 'application/json'}),
    );

    APIModel result = response.data;

    // 如果登录成功，保存 token key 以及 key_id 还会获取云端提示词
    if (result.isSuccess && result.data != null) {
      print('=== LOGIN SUCCESS DEBUG ===');
      print('开始保存登录信息...');
      await saveLogin(result.data!, username);
      print('登录信息保存完成');

      // 添加小延迟确保数据完全保存
      await Future.delayed(const Duration(milliseconds: 100));

      print('开始获取提示词...');
      try {
        await getPrompt();
        print('提示词获取成功');
      } catch (e) {
        print('获取提示词失败: $e');
        // 不要重新抛出异常，继续执行
      }
      print('开始获取模型列表...');
      await getModels();
      print('模型列表获取成功');

      print('=========================');
    }

    return result;
  }

  // 忘记密码 - 发送验证码
  Future<APIModel> forgetPassword(String email) async {
    Response response = await dio.get(
      '/forget',
      queryParameters: {"email": email},
    );
    return response.data;
  }

  // 重置密码 - 验证码确认
  Future<APIModel> resetPassword(
      String username, String password, String code) async {
    var data = json.encode({"username": username, "password": password});

    Response response = await dio.post(
      '/forget/',
      data: data,
      queryParameters: {"code_in": code},
      options: Options(headers: {'Content-Type': 'application/json'}),
    );
    return response.data;
  }

  // 查询密钥信息
  Future<APIModel> getKeyInfo(String username, String key) async {
    var data = json.encode({"username": username, "token": key});

    Response response = await dio.request(
      '/key/',
      options: Options(
        method: 'GET',
      ),
      data: data,
    );
    return response.data;
  }

  // 为密钥添加额度
  Future<void> addKeyQuota(String username, String key, String cord) async {
    var data = json.encode({
      "username": username,
      "token": key,
      "cord": cord,
    });

    await dio.request(
      '/key/',
      options: Options(
        method: 'PUT',
      ),
      data: data,
    );
  }

  // 获取更新相关信息
  Future<APIModel> getVersion() async {
    Response response = await dio.get(
      '/ai/version',
      options: Options(headers: {'Content-Type': 'application/json'}),
    );
    return response.data;
  }

  // {{ AURA-X: Add - 获取活跃公告列表. Approval: 寸止(ID:2025-08-01T23:19:21+08:00). }}
  // 获取活跃公告列表
  Future<AnnouncementResponse?> getAnnouncements() async {
    try {
      Response response = await dio.get('/announcements/active');
      APIModel result = response.data;

      // 检查是否为未登录响应
      if (result.code == 401) {
        print('ℹ️ 获取公告需要登录，跳过此操作');
        return null;
      }

      if (result.isSuccess && result.data != null) {
        print('获取公告成功，共 ${result.data['total']} 条公告');
        return AnnouncementResponse.fromJson(result.data);
      } else {
        print('获取公告失败: ${result.errorMessage}');
        return null;
      }
    } catch (e) {
      print('获取公告异常: $e');
      return null;
    }
  }
}
