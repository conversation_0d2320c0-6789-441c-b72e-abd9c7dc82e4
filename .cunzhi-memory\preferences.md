# 用户偏好设置

- 用户希望生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户希望生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户希望生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户偏好：生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户偏好：主题切换按钮使用lib\ui\theme\light_dark黑白主题色，不需要动画效果，切换后需重新打开界面生效，使用苹果风格对话框设计
- 用户偏好：生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户偏好：生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户偏好生成总结性Markdown文档，不需要测试脚本、编译或运行操作，用户会自己处理这些操作
- 用户选择方案A：基础流式显示，要求生成总结性Markdown文档，不需要测试脚本、编译或运行操作
- 用户偏好：生成总结性Markdown文档，但不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
